// 视频文件管理相关字典数据

// 任务状态
export const taskStatus = [
  {
    label: '运行中',
    value: '运行中',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '已停止',
    value: '已停止',
    raw: {
      listClass: 'info'
    }
  },
  {
    label: '异常',
    value: '异常',
    raw: {
      listClass: 'danger'
    }
  },
  {
    label: '待执行',
    value: '待执行',
    raw: {
      listClass: 'warning'
    }
  }
]

// 采集源类型
export const collectionSourceType = [
  {
    label: 'RTSP 流',
    value: 'RTSP流'
  },
  {
    label: 'FTP 文件',
    value: 'FTP文件'
  },
  {
    label: '本地设备',
    value: '本地设备'
  }
]

// 采集协议
export const collectionProtocol = [
  {
    label: 'RTSP',
    value: 'RTSP'
  },
  {
    label: 'FTP',
    value: 'FTP'
  },
  {
    label: 'SFTP',
    value: 'SFTP'
  },
  {
    label: '本地设备',
    value: '本地设备'
  },
  {
    label: 'RT SPS',
    value: 'RT_SPS'
  },
  {
    label: '其他',
    value: '其他'
  }
]

// 视频编码格式
export const videoCodecFormat = [
  {
    label: 'H.264',
    value: 'H264'
  },
  {
    label: 'H.265',
    value: 'H265'
  },
  {
    label: 'MPEG-4',
    value: 'MPEG4'
  },
  {
    label: 'AVI',
    value: 'AVI'
  },
  {
    label: 'MP4',
    value: 'MP4'
  }
]

// 分辨率选项
export const resolutionOptions = [
  {
    label: '1920x1080',
    value: '1920x1080'
  },
  {
    label: '1280x720',
    value: '1280x720'
  },
  {
    label: '640x480',
    value: '640x480'
  },
  {
    label: '320x240',
    value: '320x240'
  }
]

// 调度策略
export const scheduleStrategy = [
  {
    label: '立即执行',
    value: '立即执行'
  },
  {
    label: '定时执行',
    value: '定时执行'
  },
  {
    label: '周期执行',
    value: '周期执行'
  }
]

// 文件格式
export const fileFormat = [
  {
    label: 'MP4',
    value: 'MP4'
  },
  {
    label: 'AVI',
    value: 'AVI'
  },
  {
    label: 'FLV',
    value: 'FLV'
  },
  {
    label: 'MOV',
    value: 'MOV'
  }
]

// 文件状态
export const fileStatus = [
  {
    label: '有效',
    value: '有效',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '已删除',
    value: '已删除',
    raw: {
      listClass: 'info'
    }
  },
  {
    label: '损坏',
    value: '损坏',
    raw: {
      listClass: 'danger'
    }
  },
  {
    label: '待归档',
    value: '待归档',
    raw: {
      listClass: 'warning'
    }
  }
]

// 转码格式选项
export const transcodeFormat = [
  {
    label: 'H.264',
    value: 'H264'
  },
  {
    label: 'H.265',
    value: 'H265'
  },
  {
    label: 'MPEG-4',
    value: 'MPEG4'
  }
]

// 处理方式选项
export const processingMethod = [
  {
    label: '自动转码',
    value: 'auto_transcode'
  },
  {
    label: '标记异常',
    value: 'mark_exception'
  },
  {
    label: '不处理',
    value: 'no_process'
  }
]

// 处理优先级选项
export const processingPriority = [
  {
    label: '高',
    value: 'high'
  },
  {
    label: '中',
    value: 'medium'
  },
  {
    label: '低',
    value: 'low'
  }
]

// 处理任务状态
export const processTaskStatus = [
  {
    label: '已执行',
    value: '已执行',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '执行中',
    value: '执行中',
    raw: {
      listClass: 'warning'
    }
  },
  {
    label: '待执行',
    value: '待执行',
    raw: {
      listClass: 'info'
    }
  },
  {
    label: '异常',
    value: '异常',
    raw: {
      listClass: 'danger'
    }
  }
]

// 检测阈值预设
export const detectionThresholdPresets = [
  {
    label: '敏感',
    value: 10,
    description: '检测敏感度高，适用于高质量要求场景'
  },
  {
    label: '标准',
    value: 50,
    description: '标准检测敏感度，适用于一般场景'
  },
  {
    label: '宽松',
    value: 80,
    description: '检测敏感度低，适用于容错性要求高的场景'
  }
]

// 配置模板类型
export const configTemplateType = [
  {
    label: '高质量模式',
    value: 'high_quality',
    description: '适用于高质量视频采集，检测阈值较低'
  },
  {
    label: '标准模式',
    value: 'standard',
    description: '适用于一般质量要求的视频采集'
  },
  {
    label: '高效模式',
    value: 'efficient',
    description: '适用于大批量视频处理，检测阈值较高'
  }
]

// 萃取状态
export const extractionStatus = [
  {
    label: '进行中',
    value: '进行中',
    raw: {
      listClass: 'warning'
    }
  },
  {
    label: '成功',
    value: '成功',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '失败',
    value: '失败',
    raw: {
      listClass: 'danger'
    }
  },
  {
    label: '待处理',
    value: '待处理',
    raw: {
      listClass: 'info'
    }
  }
]

// 算法类型
export const algorithmType = [
  {
    label: '编码转换',
    value: 'encode_convert',
    raw: {
      listClass: 'primary'
    }
  },
  {
    label: '数据预处理',
    value: 'data_preprocess',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '深度加工',
    value: 'deep_process',
    raw: {
      listClass: 'warning'
    }
  }
]

// 算法状态
export const algorithmStatus = [
  {
    label: '启用',
    value: 'enabled',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '停止',
    value: 'disabled',
    raw: {
      listClass: 'info'
    }
  }
]

// 模板状态
export const templateStatus = [
  {
    label: '启用',
    value: '启用',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '停用',
    value: '停用',
    raw: {
      listClass: 'info'
    }
  }
]

// 标注主题
export const annotationTheme = [
  {
    label: '人物识别',
    value: '人物识别'
  },
  {
    label: '物品识别',
    value: '物品识别'
  },
  {
    label: '场景识别',
    value: '场景识别'
  },
  {
    label: '混合标注',
    value: '混合标注'
  }
]

// 调度方式
export const scheduleType = [
  {
    label: '定时调度',
    value: '定时调度'
  },
  {
    label: '单次调度',
    value: '单次调度'
  },
  {
    label: '周期调度',
    value: '周期调度'
  }
]

// 标注任务状态
export const annotationTaskStatus = [
  {
    label: '运行中',
    value: '运行中',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '已完成',
    value: '已完成',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '已停止',
    value: '已停止',
    raw: {
      listClass: 'info'
    }
  },
  {
    label: '待执行',
    value: '待执行',
    raw: {
      listClass: 'warning'
    }
  }
]

// 算子组合类型
export const operatorCombinationType = [
  {
    label: '串联',
    value: '串联',
    raw: {
      listClass: 'primary'
    }
  },
  {
    label: '并联',
    value: '并联',
    raw: {
      listClass: 'success'
    }
  }
]

// 算子链状态
export const operatorChainStatus = [
  {
    label: '启用',
    value: '启用',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '禁用',
    value: '禁用',
    raw: {
      listClass: 'info'
    }
  }
]

// 算子类型
export const operatorTypes = [
  {
    label: '车牌号识别算子',
    value: '车牌号识别算子'
  },
  {
    label: '车辆内容理解算子',
    value: '车辆内容理解算子'
  },
  {
    label: '人脸识别算子',
    value: '人脸识别算子'
  },
  {
    label: '物体检测算子',
    value: '物体检测算子'
  },
  {
    label: '行为分析算子',
    value: '行为分析算子'
  },
  {
    label: '场景理解算子',
    value: '场景理解算子'
  },
  {
    label: '文字识别算子',
    value: '文字识别算子'
  },
  {
    label: '语音识别算子',
    value: '语音识别算子'
  }
]

// 索引状态
export const indexStatus = [
  {
    label: '正常',
    value: '正常',
    raw: {
      listClass: 'success'
    }
  },
  {
    label: '需优化',
    value: '需优化',
    raw: {
      listClass: 'warning'
    }
  }
]

// 搜索类型
export const searchType = [
  {
    label: '向量搜索',
    value: '向量搜索'
  },
  {
    label: '全文搜索',
    value: '全文搜索'
  },
  {
    label: '混合搜索',
    value: '混合搜索'
  }
]
